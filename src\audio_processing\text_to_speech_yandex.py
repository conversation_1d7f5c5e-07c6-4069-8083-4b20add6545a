from typing import List
import os
import io
import grpc
import scipy.io.wavfile
import numpy as np
from pydub import AudioSegment
from tenacity import retry, stop_after_attempt, wait_exponential

from yandex.cloud.ai.tts.v3 import tts_pb2
from yandex.cloud.ai.tts.v3 import tts_service_pb2_grpc

from src.utils.logger import logger
from src.audio_processing.text_to_speech import TextToSpeech, Voice

try:
    from src.api.yandex_token_manager import YandexTokenManager

    TOKEN_MANAGER_AVAILABLE = True
except ImportError:
    TOKEN_MANAGER_AVAILABLE = False


class TextToSpeechYandex(TextToSpeech):
    """
    text to speech implementation using yandex speechkit.

    requires iam token and folder id to be set as environment variables
    yandex_iam_token and yandex_folder_id or passed during initialization.
    """

    # define gender constants (if not defined in texttospeech)
    _SSML_FEMALE = "female"
    _SSML_MALE = "male"

    # mapping of language codes to yandex voice ids
    # supports both iso 639-1 (2-letter) and iso 639-3 (3-letter) codes
    VOICE_MAPPING = {
        # iso 639-1 codes (backward compatibility)
        "ru": [
            Voice(name="alena", gender=_SSML_FEMALE),
            Voice(name="filipp", gender=_SSML_MALE),
            Voice(name="jane", gender=_SSML_FEMALE),
            Voice(name="omazh", gender=_SSML_FEMALE),
            Voice(name="zahar", gender=_SSML_MALE),
            Voice(name="lola", gender=_SSML_FEMALE),
        ],
        "en": [
            Voice(name="john", gender=_SSML_MALE),
            Voice(name="nick", gender=_SSML_MALE),
        ],
        "tr": [
            Voice(name="silaerkan", gender=_SSML_MALE),
            Voice(name="erkanyavas", gender=_SSML_MALE),
        ],
        "uz": [
            Voice(name="nigora", gender=_SSML_FEMALE),
            Voice(name="lola", gender=_SSML_FEMALE),
            Voice(name="yulduz", gender=_SSML_FEMALE),
            Voice(name="zamira", gender=_SSML_FEMALE),
        ],
        # iso 639-3 codes (preferred standard)
        "rus": [
            Voice(name="alena", gender=_SSML_FEMALE),
            Voice(name="filipp", gender=_SSML_MALE),
            Voice(name="jane", gender=_SSML_FEMALE),
            Voice(name="omazh", gender=_SSML_FEMALE),
            Voice(name="zahar", gender=_SSML_MALE),
            Voice(name="lola", gender=_SSML_FEMALE),
        ],
        "eng": [
            Voice(name="john", gender=_SSML_MALE),
            Voice(name="nick", gender=_SSML_MALE),
        ],
        "uzb": [
            Voice(name="nigora", gender=_SSML_FEMALE),
            Voice(name="lola", gender=_SSML_FEMALE),
            Voice(name="yulduz", gender=_SSML_FEMALE),
            Voice(name="zamira", gender=_SSML_FEMALE),
        ],
    }

    # mapping from iso language codes to yandex language codes
    # supports both iso 639-1 (2-letter) and iso 639-3 (3-letter) codes
    LANGUAGE_MAPPING = {
        # iso 639-1 codes (backward compatibility)
        "ru": "ru-RU",
        "en": "en-US",
        "uz": "uz-UZ",
        # iso 639-3 codes (preferred standard)
        "rus": "ru-RU",
        "eng": "en-US",
        "uzb": "uz-UZ",
    }

    def __init__(self, iam_token=None, folder_id=None, audio_quality="maximum"):
        """
        initialize the yandex tts client.

        args:
            iam_token: optional yandex iam token. if not provided, will try to get from
                       environment variable yandex_iam_token.
            folder_id: optional yandex folder id. if not provided, will try to get from
                       environment variable yandex_folder_id.
            audio_quality: audio quality preset - standard, high, or maximum
        """
        super().__init__()
        self.iam_token = iam_token or os.environ.get("YANDEX_IAM_TOKEN")
        self.folder_id = folder_id or os.environ.get("YANDEX_FOLDER_ID")
        self.audio_quality = audio_quality
        self.token_manager = None

        # try to initialize token manager for automatic token refresh
        if TOKEN_MANAGER_AVAILABLE:
            try:
                # get oauth token from environment if not provided
                oauth_token = os.environ.get("YANDEX_0AUTH_TOKEN")
                if oauth_token:
                    self.token_manager = YandexTokenManager(oauth_token=oauth_token)
                    logger().info(
                        "using yandextokenmanager for automatic token refresh"
                    )
            except Exception as e:
                logger().error(f"failed to initialize token manager: {e}")
                self.token_manager = None

        if not self.get_current_token() or not self.folder_id:
            logger().warning(
                "yandex iam token or folder id not provided. set yandex_iam_token and yandex_folder_id environment variables or pass parameters."
            )

    def get_current_token(self) -> str:
        """
        get the current valid iam token, using token manager if available.

        returns:
            current valid iam token
        """
        if self.token_manager:
            return self.token_manager.get_token()
        return self.iam_token or os.environ.get("YANDEX_IAM_TOKEN", "")

    def get_available_voices(self, language_code: str) -> List[Voice]:
        """
        return list of available voices for the specified language.

        args:
            language_code: iso language code (e.g., 'en', 'ru')

        returns:
            list of voice objects available for the language
        """
        if language_code in self.VOICE_MAPPING:
            return self.VOICE_MAPPING[language_code]
        else:
            # if language not in mapping, return empty list
            logger().warning(f"no voices found for language code: {language_code}")
            return []

    def _split_text(self, text: str, max_chars: int = 200) -> List[str]:
        """
        split text into chunks at sentence boundaries, respecting max length.

        args:
            text: text to split
            max_chars: maximum characters per chunk

        returns:
            list of text chunks
        """
        # split by sentences first
        sentences = text.replace("!", ".").replace("?", ".").split(".")
        chunks = []
        current_chunk = []
        current_length = 0

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # if single sentence is too long, split by words
            if len(sentence) > max_chars:
                words = sentence.split()
                current_words = []
                current_word_length = 0

                for word in words:
                    word_length = len(word) + 1
                    if current_word_length + word_length > max_chars and current_words:
                        chunks.append(" ".join(current_words))
                        current_words = []
                        current_word_length = 0
                    current_words.append(word)
                    current_word_length += word_length

                if current_words:
                    chunks.append(" ".join(current_words))
            else:
                # try to add sentence to current chunk
                if current_length + len(sentence) + 1 <= max_chars:
                    current_chunk.append(sentence)
                    current_length += len(sentence) + 1
                else:
                    # save current chunk and start new one
                    if current_chunk:
                        chunks.append(". ".join(current_chunk) + ".")
                    current_chunk = [sentence]
                    current_length = len(sentence) + 1

        # add the last chunk if it exists
        if current_chunk:
            chunks.append(". ".join(current_chunk) + ".")

        # additional safety check: if any chunk is still too long, split it further
        final_chunks = []
        for chunk in chunks:
            if len(chunk) > max_chars:
                # split into smaller pieces
                while chunk:
                    final_chunks.append(chunk[:max_chars].strip())
                    chunk = chunk[max_chars:].strip()
            else:
                final_chunks.append(chunk)

        return final_chunks

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True,
    )
    def _synthesize_with_retry(
        self, text: str, voice: str, speaking_rate: float
    ) -> AudioSegment:
        """
        synthesize speech with retry logic for handling long text.

        args:
            text: text to synthesize
            voice: voice name to use
            speaking_rate: speech rate

        returns:
            audiosegment with the synthesized speech
        """
        try:
            # additional safety check - if text is too long, split it
            if len(text) > 200:  # hard limit of 200 characters
                logger().debug(
                    f"text too long ({len(text)} chars), splitting into smaller chunks"
                )
                chunks = self._split_text(text, 200)
                combined = None
                for chunk in chunks:
                    audio = self._synthesize(chunk, voice, speaking_rate)
                    if combined is None:
                        combined = audio
                    else:
                        combined = combined + audio
                return combined
            return self._synthesize(text, voice, speaking_rate)
        except Exception as e:
            if "too long text" in str(e).lower():
                logger().warning(
                    f"chunk too long ({len(text)} chars), retrying with smaller chunks"
                )
                raise  # this will trigger retry with smaller chunks
            logger().error(f"tts error: {str(e)}")
            raise

    def _synthesize(self, text: str, voice: str, speaking_rate: float) -> AudioSegment:
        """
        synthesize speech using yandex tts service.

        args:
            text: text to synthesize
            voice: voice name to use
            speaking_rate: speech rate

        returns:
            audiosegment with the synthesized speech
        """
        # ensure text is not too long
        if len(text) > 200:
            raise ValueError(f"text too long ({len(text)} chars)")

        # yandex already uses wav format which is high quality
        # for now, keep using wav format regardless of quality setting
        # todo: investigate yandex raw audio format options for higher quality
        request = tts_pb2.UtteranceSynthesisRequest(
            text=text,
            output_audio_spec=tts_pb2.AudioFormatOptions(
                container_audio=tts_pb2.ContainerAudio(
                    container_audio_type=tts_pb2.ContainerAudio.WAV
                )
            ),
            hints=[
                tts_pb2.Hints(voice=voice),
                tts_pb2.Hints(speed=speaking_rate),
            ],
            loudness_normalization_type=tts_pb2.UtteranceSynthesisRequest.LUFS,
        )

        # use ssl credentials
        cred = grpc.ssl_channel_credentials()
        channel = grpc.secure_channel("tts.api.cloud.yandex.net:443", cred)
        stub = tts_service_pb2_grpc.SynthesizerStub(channel)

        try:
            # get current token if we have a token manager
            token_to_use = self.get_current_token()

            it = stub.UtteranceSynthesis(
                request,
                metadata=(
                    ("authorization", f"Bearer {token_to_use}"),
                    ("x-folder-id", self.folder_id),
                ),
            )

            audio = io.BytesIO()
            for response in it:
                audio.write(response.audio_chunk.data)
            audio.seek(0)
            return AudioSegment.from_wav(audio)
        except grpc._channel._Rendezvous as err:
            logger().error(
                f"error code {err._state.code}, message: {err._state.details}"
            )
            raise err
        finally:
            channel.close()

    def _convert_text_to_speech_primary(
        self,
        *,
        assigned_voice: str,
        target_language: str,
        output_filename: str,
        text: str,
        speed: float,
    ) -> str:
        """
        convert text to speech using yandex speechkit api.

        args:
            assigned_voice: voice name to use
            target_language: iso language code
            output_filename: path to save the audio file
            text: text to convert to speech
            speed: speech rate (0.5 to 2.0)

        returns:
            path to the created audio file
        """
        logger().debug(f"texttospeechyandex._convert_text_to_speech: {text}")

        try:
            # split text into chunks
            text_chunks = self._split_text(text)
            logger().debug(f"split text into {len(text_chunks)} chunks")

            # synthesize each chunk and combine
            combined_audio = None
            for i, chunk in enumerate(text_chunks, 1):
                logger().debug(
                    f"processing chunk {i}/{len(text_chunks)} ({len(chunk)} chars)"
                )
                chunk_audio = self._synthesize_with_retry(chunk, assigned_voice, speed)
                if combined_audio is None:
                    combined_audio = chunk_audio
                else:
                    combined_audio = combined_audio + chunk_audio

            # export to mp3
            mp3_file = output_filename
            combined_audio.export(mp3_file, format="mp3")

            logger().debug(
                f"texttospeechyandex._convert_text_to_speech: output_filename: '{output_filename}'"
            )
            return output_filename

        except Exception as e:
            logger().error(f"failed to synthesize speech: {str(e)}")
            # create a fallback silent audio if the api call fails
            sampling_rate = 16000
            duration_seconds = 1
            output_np = np.ones(sampling_rate * duration_seconds, dtype=np.int16)
            wav_file = output_filename.replace(".mp3", ".wav")
            scipy.io.wavfile.write(wav_file, rate=sampling_rate, data=output_np)
            self._convert_to_mp3(wav_file, output_filename)
            return output_filename

    def get_languages(self) -> List[str]:
        """
        return list of supported language codes.

        returns:
            list of iso language codes supported by yandex speechkit
        """
        # return keys from the language mapping
        return list(self.LANGUAGE_MAPPING.keys())
