from typing import List, Dict
import os
import io
import requests
import scipy.io.wavfile
import numpy as np
from pydub import AudioSegment
from tenacity import retry, stop_after_attempt, wait_exponential

from src.utils.logger import logger
from src.audio_processing.text_to_speech import TextToSpeech, Voice


class TextToSpeechAzure(TextToSpeech):
    """
    text to speech implementation using microsoft azure cognitive services.

    requires subscription key and region to be set as environment variables
    azure_speech_key and azure_speech_region or passed during initialization.
    """

    # define gender constants
    _SSML_FEMALE = "female"
    _SSML_MALE = "male"

    # mapping of language codes to azure voice ids
    # this is a subset of available voices, can be expanded as needed
    VOICE_MAPPING = {
        "en": [
            Voice(name="en-US-AriaNeural", gender=_SSML_FEMALE),
            Voice(name="en-US-GuyNeural", gender=_SSML_MALE),
            Voice(name="en-GB-SoniaNeural", gender=_SSML_FEMALE),
            Voice(name="en-GB-RyanNeural", gender=_SSML_MALE),
        ],
        "ru": [
            Voice(name="ru-RU-SvetlanaNeural", gender=_SSML_FEMALE),
            Voice(name="ru-RU-DmitryNeural", gender=_SSML_MALE),
        ],
        "uz": [
            Voice(name="uz-UZ-MadinaNeural", gender=_SSML_FEMALE),
            Voice(name="uz-UZ-SardorNeural", gender=_SSML_MALE),
        ],
    }

    # mapping from iso language codes to azure language codes
    # supports both iso 639-1 (2-letter) and iso 639-3 (3-letter) codes
    LANGUAGE_MAPPING = {
        # iso 639-1 codes (backward compatibility)
        "en": "en-US",  # default to us english
        "ru": "ru-RU",
        "uz": "uz-UZ",
        # iso 639-3 codes (preferred standard)
        "eng": "en-US",
        "rus": "ru-RU",
        "uzb": "uz-UZ",
    }

    def __init__(self, subscription_key=None, region=None, audio_quality="maximum"):
        """
        initialize the azure tts client.

        args:
            subscription_key: optional azure speech subscription key. if not provided,
                              will try to get from environment variable azure_speech_key.
            region: optional azure region. if not provided, will try to get from
                    environment variable azure_speech_region.
            audio_quality: audio quality preset - standard, high, or maximum
        """
        super().__init__()
        self.subscription_key = subscription_key or os.environ.get("AZURE_SPEECH_KEY")
        self.region = region or os.environ.get("AZURE_SPEECH_REGION")
        self.audio_quality = audio_quality

        if not self.subscription_key or not self.region:
            logger().warning(
                "azure speech key or region not provided. set azure_speech_key and azure_speech_region environment variables or pass parameters."
            )

        # azure tts endpoint
        self.endpoint = (
            f"https://{self.region}.tts.speech.microsoft.com/cognitiveservices/v1"
        )

        # select audio format based on quality setting
        audio_formats = {
            "standard": "audio-16khz-128kbitrate-mono-mp3",
            "high": "audio-24khz-160kbitrate-mono-mp3",
            "maximum": "audio-48khz-192kbitrate-mono-mp3",
        }

        output_format = audio_formats.get(audio_quality, audio_formats["maximum"])

        # headers for azure tts api
        self.headers = {
            "Ocp-Apim-Subscription-Key": self.subscription_key,
            "Content-Type": "application/ssml+xml",
            "X-Microsoft-OutputFormat": output_format,
            "User-Agent": "open-dubbing-tts-client",
        }

    def get_available_voices(self, language_code: str) -> List[Voice]:
        """
        return list of available voices for the specified language.

        args:
            language_code: iso language code (e.g., 'en', 'ru', 'uzb')

        returns:
            list of voice objects available for the language
        """
        # first try direct lookup in voice mapping
        if language_code in self.VOICE_MAPPING:
            return self.VOICE_MAPPING[language_code]

        # if not found, try to convert using language mapping
        if language_code in self.LANGUAGE_MAPPING:
            azure_lang_code = self.LANGUAGE_MAPPING[language_code]
            # extract the base language code (e.g., "uz-UZ" -> "uz")
            base_lang_code = azure_lang_code.split("-")[0]
            if base_lang_code in self.VOICE_MAPPING:
                return self.VOICE_MAPPING[base_lang_code]

        # if still not found, return empty list
        logger().warning(f"no voices found for language code: {language_code}")
        return []

    def _split_text(self, text: str, max_chars: int = 1000) -> List[str]:
        """
        split text into chunks at sentence boundaries, respecting max length.

        args:
            text: text to split
            max_chars: maximum characters per chunk

        returns:
            list of text chunks
        """
        # split by sentences first
        sentences = text.replace("!", ".").replace("?", ".").split(".")
        chunks = []
        current_chunk = []
        current_length = 0

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # if single sentence is too long, split by words
            if len(sentence) > max_chars:
                words = sentence.split()
                current_words = []
                current_word_length = 0

                for word in words:
                    word_length = len(word) + 1
                    if current_word_length + word_length > max_chars and current_words:
                        chunks.append(" ".join(current_words))
                        current_words = []
                        current_word_length = 0
                    current_words.append(word)
                    current_word_length += word_length

                if current_words:
                    chunks.append(" ".join(current_words))
            else:
                # try to add sentence to current chunk
                if current_length + len(sentence) + 1 <= max_chars:
                    current_chunk.append(sentence)
                    current_length += len(sentence) + 1
                else:
                    # save current chunk and start new one
                    if current_chunk:
                        chunks.append(". ".join(current_chunk) + ".")
                    current_chunk = [sentence]
                    current_length = len(sentence) + 1

        # add the last chunk if it exists
        if current_chunk:
            chunks.append(". ".join(current_chunk) + ".")

        # additional safety check: if any chunk is still too long, split it further
        final_chunks = []
        for chunk in chunks:
            if len(chunk) > max_chars:
                # split into smaller pieces
                while chunk:
                    final_chunks.append(chunk[:max_chars].strip())
                    chunk = chunk[max_chars:].strip()
            else:
                final_chunks.append(chunk)

        return final_chunks

    def _create_ssml(self, text: str, voice_name: str, speaking_rate: str) -> str:
        """
        create ssml for azure tts api.

        args:
            text: text to synthesize
            voice_name: voice name to use
            speaking_rate: speech rate as string (e.g., "normal", "+10%", "-20%")

        returns:
            ssml string for azure tts api
        """
        # escape xml special characters in text
        escaped_text = (
            text.replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace('"', "&quot;")
            .replace("'", "&apos;")
        )

        # extract language code from voice name (e.g., "uz-UZ-SardorNeural" -> "uz-UZ")
        lang_code = (
            "-".join(voice_name.split("-")[:2]) if "-" in voice_name else "en-US"
        )

        # ensure speaking_rate is properly formatted for azure
        if speaking_rate in ["default", "x-slow", "slow", "medium", "fast", "x-fast"]:
            rate_value = speaking_rate if speaking_rate != "default" else "medium"
        elif speaking_rate.endswith("%"):
            rate_value = speaking_rate
        else:
            # fallback to normal if rate format is unexpected
            rate_value = "normal"

        ssml = f"""<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xmlns:mstts="https://www.w3.org/2001/mstts" xml:lang="{lang_code}">
    <voice name="{voice_name}">
        <prosody rate="{rate_value}">
            {escaped_text}
        </prosody>
    </voice>
</speak>"""
        return ssml

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True,
    )
    def _synthesize_with_retry(
        self, text: str, voice: str, speaking_rate: float
    ) -> AudioSegment:
        """
        synthesize speech with retry logic for handling long text.

        args:
            text: text to synthesize
            voice: voice name to use
            speaking_rate: speech rate as a string (e.g., "1.0")

        returns:
            audiosegment with the synthesized speech
        """
        try:
            # convert speaking_rate from float to string format
            rate_str = f"{speaking_rate:+.1f}" if speaking_rate != 1.0 else "1.0"
            # for azure, rate strings should be like: "-10%", "+50%", or "normal"
            if speaking_rate < 1.0:
                # convert to percentage reduction (e.g., 0.8 -> "-20%")
                percent_change = int((1.0 - speaking_rate) * 100)
                rate_str = f"-{percent_change}%"
            elif speaking_rate > 1.0:
                # convert to percentage increase (e.g., 1.5 -> "+50%")
                percent_change = int((speaking_rate - 1.0) * 100)
                rate_str = f"+{percent_change}%"
            else:
                # azure doesn't accept 'normal'; use 'default' (equivalent to no change)
                rate_str = "default"

            # additional safety check - if text is too long, split it
            if len(text) > 1000:  # azure can handle longer text than yandex
                logger().debug(
                    f"text too long ({len(text)} chars), splitting into smaller chunks"
                )
                chunks = self._split_text(text, 1000)
                combined = None
                for chunk in chunks:
                    audio = self._synthesize(chunk, voice, rate_str)
                    if combined is None:
                        combined = audio
                    else:
                        combined = combined + audio
                return combined
            return self._synthesize(text, voice, rate_str)
        except Exception as e:
            if "request body too large" in str(e).lower():
                logger().warning(
                    f"chunk too long ({len(text)} chars), retrying with smaller chunks"
                )
                raise  # this will trigger retry with smaller chunks
            logger().error(f"tts error: {str(e)}")
            raise

    def _synthesize(self, text: str, voice: str, speaking_rate: str) -> AudioSegment:
        """
        synthesize speech using azure tts service.

        args:
            text: text to synthesize
            voice: voice name to use
            speaking_rate: speech rate as a string (e.g., "+10%", "-20%", "normal")

        returns:
            audiosegment with the synthesized speech
        """
        # create ssml
        ssml = self._create_ssml(text, voice, speaking_rate)

        logger().debug(
            f"azure tts request - voice: {voice}, rate: {speaking_rate}, text length: {len(text)}"
        )

        try:
            # make request to azure tts api
            response = requests.post(
                self.endpoint, headers=self.headers, data=ssml.encode("utf-8")
            )

            # check if request was successful
            if response.status_code != 200:
                logger().error(
                    f"azure tts api error: {response.status_code}, {response.text}"
                )
                logger().error(f"request ssml: {ssml}")
                logger().error(f"request headers: {self.headers}")
                logger().error(f"voice: {voice}, speaking_rate: {speaking_rate}")
                logger().error(f"text preview: {text[:100]}...")
                raise Exception(
                    f"azure tts api error: {response.status_code}, {response.text}"
                )

            # convert response to audiosegment
            audio = io.BytesIO(response.content)
            return AudioSegment.from_mp3(audio)

        except Exception as e:
            logger().error(f"error synthesizing speech: {str(e)}")
            raise e

    def _convert_text_to_speech_primary(
        self,
        *,
        assigned_voice: str,
        target_language: str,
        output_filename: str,
        text: str,
        speed: float,
    ) -> str:
        """
        convert text to speech using azure speech services.

        args:
            assigned_voice: voice name to use
            target_language: iso language code
            output_filename: path to save the audio file
            text: text to convert to speech
            speed: speech rate (0.5 to 2.0)

        returns:
            path to the created audio file
        """
        logger().debug(f"texttospeechazure._convert_text_to_speech: {text}")

        try:
            # split text into chunks
            text_chunks = self._split_text(text)
            logger().debug(f"split text into {len(text_chunks)} chunks")

            # synthesize each chunk and combine
            combined_audio = None
            for i, chunk in enumerate(text_chunks, 1):
                logger().debug(
                    f"processing chunk {i}/{len(text_chunks)} ({len(chunk)} chars)"
                )
                chunk_audio = self._synthesize_with_retry(chunk, assigned_voice, speed)
                if combined_audio is None:
                    combined_audio = chunk_audio
                else:
                    combined_audio = combined_audio + chunk_audio

            # export to mp3
            combined_audio.export(output_filename, format="mp3")

            logger().debug(
                f"texttospeechazure._convert_text_to_speech: output_filename: '{output_filename}'"
            )
            return output_filename

        except Exception as e:
            logger().error(f"failed to synthesize speech: {str(e)}")
            # create a fallback silent audio if the api call fails
            sampling_rate = 16000
            duration_seconds = 1
            output_np = np.ones(sampling_rate * duration_seconds, dtype=np.int16)
            wav_file = output_filename.replace(".mp3", ".wav")
            scipy.io.wavfile.write(wav_file, rate=sampling_rate, data=output_np)
            self._convert_to_mp3(wav_file, output_filename)
            return output_filename

    def get_languages(self) -> List[str]:
        """
        return list of supported language codes.

        returns:
            list of iso language codes supported by azure speech services
        """
        # return keys from the language mapping
        return list(self.LANGUAGE_MAPPING.keys())

    def list_all_voices(self) -> List[Dict]:
        """
        get full list of available voices from azure.

        returns:
            list of voice details dictionaries
        """
        try:
            voice_list_url = f"https://{self.region}.tts.speech.microsoft.com/cognitiveservices/voices/list"
            response = requests.get(
                voice_list_url,
                headers={"Ocp-Apim-Subscription-Key": self.subscription_key},
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger().error(
                    f"failed to get voice list: {response.status_code}, {response.text}"
                )
                return []
        except Exception as e:
            logger().error(f"error getting voice list: {str(e)}")
            return []
