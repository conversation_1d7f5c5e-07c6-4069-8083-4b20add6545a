{"_comment": "aizen platform dubbing configuration - async assemblyai example", "_description": "this configuration demonstrates using async assemblyai for speech-to-text with 2-5x performance improvements, advanced features like speaker diarization, and intelligent parallel processing.", "input": {"_comment": "input media processing settings", "source_path": "https://inspired.fra1.cdn.digitaloceanspaces.com/aizen/doctor_short.mp4", "source_format": "mp4", "temp_dir": "./tmp", "silence": {"_comment": "silence detection for natural speech segmentation", "method": "energy", "threshold": 0.35, "padding_seconds": 0.5}, "separation": {"_comment": "audio source separation using demucs - htdemucs_ft provides best quality but is 4x slower than htdemucs", "model": "htdemucs_ft", "stems": ["vocals", "accompaniment"], "output_format": "wav", "segment_duration": 7.8, "overlap": 0.25, "shifts": 10, "split": true, "device": "cuda", "jobs": 1, "clip_mode": "rescale", "float32": false, "int24": false, "two_stems": null, "extra_args": []}}, "translation": {"_comment": "translation pipeline configuration using google gemini models", "source_lang": "eng", "target_lang": "uzb", "analyzer": {"_comment": "gemini-2.5-flash is faster and cheaper than gemini-2.5-pro while maintaining good quality", "provider": "gemini", "model": "gemini-2.5-flash", "temperature": 0.2, "top_p": 0.95}, "segment_processor": {"_comment": "same model for consistency - use gemini-2.5-pro for highest quality if budget allows", "provider": "gemini", "model": "gemini-2.5-flash", "temperature": 0.2, "top_p": 0.95}}, "processing": {"_comment": "audio processing and speech analysis configuration with async assemblyai", "skip_diarization": false, "enhanced_segmentation": true, "max_segment_duration": 30.0, "async_transcription": true, "_async_transcription_comment": "use async assemblyai for 2-5x performance improvement - parallel processing of audio chunks", "max_concurrent_transcriptions": 10, "_max_concurrent_comment": "number of parallel transcription jobs - adjust based on assemblyai api limits", "update_mode": false, "run_id": null}, "performance": {"_comment": "resource management and processing limits optimized for async assemblyai", "max_workers": 10, "_max_workers_comment": "increased for async transcription - optimal for parallel assemblyai processing", "rate_limit_per_sec": 10, "_rate_limit_comment": "assemblyai rate limit - token bucket algorithm with burst support", "translation_workers": 20, "_translation_workers_comment": "max parallel workers for gemini translation - with 1k rpm we can safely use 20+ workers", "use_gpu": true, "max_memory_gb": null, "assemblyai_burst_size": 20, "_assemblyai_burst_comment": "burst capacity for assemblyai api calls - allows initial rapid submission"}, "models": {"_comment": "text-to-speech and transcription model settings - using async assemblyai for enhanced performance", "tts_provider": "azure", "stt_provider": "assemblyai", "_stt_provider_comment": "assemblyai with async processing for 2-5x speedup and advanced features", "assemblyai_model": "best", "_assemblyai_model_comment": "assemblyai speech model: best, nano, slam-1, universal", "assemblyai_features": {"speaker_labels": true, "language_detection": true, "auto_highlights": false, "sentiment_analysis": false, "entity_detection": false}, "_assemblyai_features_comment": "advanced assemblyai features - speaker_labels and language_detection recommended", "transcription_chunk_size": 30.0, "timestamp_window": 0.5, "tts_voice": "uz-UZ-MadinaNeural", "temperature": 0.7, "target_language_region": "", "hugging_face_token": null}, "output": {"_comment": "output file configuration and logging settings - debug level provides detailed information for development and troubleshooting", "output_dir": "./outputs", "naming_pattern": "{basename}_{lang}.{ext}", "log_level": "debug", "log_format": "%(asctime)s | %(levelname)s | %(message)s", "video_encoder": "pydub", "original_subtitles": false, "dubbed_subtitles": false, "clean_intermediate_files": false}}