import os
import shutil
import subprocess
import tempfile
from enum import Enum
from typing import List, Optional, Dict

from src.utils.logger import logger


class HWAccel(Enum):
    none = "none"
    cuda = "cuda"
    nvenc = "nvenc"
    qsv = "qsv"
    vaapi = "vaapi"
    amf = "amf"


class FFmpeg:
    def __init__(self, hwaccel: Optional[HWAccel] = None):
        self.hwaccel = hwaccel

    def _add_hwaccel_args(self, command: List[str]) -> List[str]:
        """add hardware acceleration arguments to the command if hwaccel is set"""
        if not self.hwaccel or self.hwaccel == HWAccel.none:
            return command

        # identify insertion point (after ffmpeg but before other args)
        insert_idx = 1 if command[0] == "ffmpeg" else 0

        if self.hwaccel == HWAccel.cuda:
            command[insert_idx:insert_idx] = ["-hwaccel", "cuda"]
        elif self.hwaccel == HWAccel.nvenc:
            command[insert_idx:insert_idx] = ["-hwaccel", "nvenc"]
        elif self.hwaccel == HWAccel.qsv:
            command[insert_idx:insert_idx] = ["-hwaccel", "qsv"]
        elif self.hwaccel == HWAccel.vaapi:
            command[insert_idx:insert_idx] = ["-hwaccel", "vaapi"]
        elif self.hwaccel == HWAccel.amf:
            command[insert_idx:insert_idx] = ["-hwaccel", "amf"]

        return command

    def _get_hw_encoder(self, codec: str = "h264") -> Optional[str]:
        """get the hardware-accelerated encoder based on hwaccel and codec"""
        if not self.hwaccel or self.hwaccel == HWAccel.none:
            return None

        encoders = {
            "h264": {
                HWAccel.cuda: "h264_nvenc",
                HWAccel.nvenc: "h264_nvenc",
                HWAccel.qsv: "h264_qsv",
                HWAccel.vaapi: "h264_vaapi",
                HWAccel.amf: "h264_amf",
            },
            "hevc": {
                HWAccel.cuda: "hevc_nvenc",
                HWAccel.nvenc: "hevc_nvenc",
                HWAccel.qsv: "hevc_qsv",
                HWAccel.vaapi: "hevc_vaapi",
                HWAccel.amf: "hevc_amf",
            },
        }

        return encoders.get(codec, {}).get(self.hwaccel)

    def _run(self, *, command: List[str], fail: bool = True):
        # add hardware acceleration arguments
        command = self._add_hwaccel_args(command)

        with open(os.devnull, "wb") as devnull:
            try:
                logger().debug(f"running command: {' '.join(command)}")
                result = subprocess.run(command, stdout=devnull, stderr=subprocess.PIPE)
                if result.returncode != 0:
                    raise subprocess.CalledProcessError(result.returncode, command)
            except subprocess.CalledProcessError as e:
                logger().error(
                    f"error running command: {command} failed with exit code {e.returncode} and output '{result.stderr.decode().strip()}'"
                )
                if fail:
                    raise

    def convert_to_format(
        self,
        *,
        source: str,
        target: str,
        codec: str = "h264",
        copy_video: bool = False,
        audio_quality: str = "maximum",
        audio_bitrate: str | None = None,
        audio_sample_rate: str | None = None,
    ):
        """convert video to target format with high-quality audio encoding options"""
        cmd = [
            "ffmpeg",
            "-hide_banner",
            "-y",
            "-i",
            source,
        ]

        if copy_video:
            # copy video stream without re-encoding (much faster)
            cmd.extend(["-c:v", "copy"])
        else:
            # use hardware encoder if available
            hw_encoder = self._get_hw_encoder(codec)
            if hw_encoder:
                cmd.extend(["-c:v", hw_encoder])

        # add high-quality audio encoding parameters
        if target.lower().endswith((".mp3", ".wav", ".flac", ".aac")):
            # determine audio codec and quality settings
            if target.lower().endswith(".mp3"):
                cmd.extend(["-c:a", "libmp3lame"])
                # set high-quality mp3 encoding
                if audio_quality == "maximum":
                    cmd.extend(
                        [
                            "-b:a",
                            audio_bitrate or "320k",
                            "-ar",
                            audio_sample_rate or "48000",
                        ]
                    )
                elif audio_quality == "high":
                    cmd.extend(
                        [
                            "-b:a",
                            audio_bitrate or "256k",
                            "-ar",
                            audio_sample_rate or "44100",
                        ]
                    )
                else:  # standard
                    cmd.extend(
                        [
                            "-b:a",
                            audio_bitrate or "192k",
                            "-ar",
                            audio_sample_rate or "44100",
                        ]
                    )
                # use highest quality mp3 encoding
                cmd.extend(["-q:a", "0"])  # variable bitrate, highest quality
            elif target.lower().endswith(".wav"):
                cmd.extend(["-c:a", "pcm_s24le"])  # 24-bit pcm for maximum quality
                cmd.extend(["-ar", audio_sample_rate or "48000"])
            elif target.lower().endswith(".flac"):
                cmd.extend(["-c:a", "flac"])
                cmd.extend(["-compression_level", "12"])  # maximum compression
                cmd.extend(["-ar", audio_sample_rate or "48000"])
            elif target.lower().endswith(".aac"):
                cmd.extend(["-c:a", "aac"])
                if audio_quality == "maximum":
                    cmd.extend(
                        [
                            "-b:a",
                            audio_bitrate or "256k",
                            "-ar",
                            audio_sample_rate or "48000",
                        ]
                    )
                elif audio_quality == "high":
                    cmd.extend(
                        [
                            "-b:a",
                            audio_bitrate or "192k",
                            "-ar",
                            audio_sample_rate or "44100",
                        ]
                    )
                else:  # standard
                    cmd.extend(
                        [
                            "-b:a",
                            audio_bitrate or "128k",
                            "-ar",
                            audio_sample_rate or "44100",
                        ]
                    )

        cmd.append(target)

        self._run(command=cmd)

    def extract_audio(
        self,
        *,
        video_file: str,
        audio_output: str,
        copy_audio: bool = True,
        audio_format: str | None = None,
    ):
        """extract audio from video with optimized approach - try stream copy first, then format conversion"""

        # if we need a specific format (like wav), try stream copy first for speed
        if audio_format and not copy_audio:
            # first attempt: try to extract with stream copy to a temp file, then convert if needed
            temp_audio = audio_output.replace(".wav", "_temp.aac").replace(
                ".mp3", "_temp.aac"
            )

            try:
                # step 1: fast stream copy extraction
                logger().debug(f"attempting fast audio stream copy from {video_file}")
                copy_cmd = [
                    "ffmpeg",
                    "-hide_banner",
                    "-y",
                    "-i",
                    video_file,
                    "-vn",  # no video
                    "-acodec",
                    "copy",
                    temp_audio,
                ]

                self._run(command=copy_cmd, fail=True)

                # step 2: convert to target format if needed
                if temp_audio != audio_output:
                    logger().debug(f"converting audio to {audio_format} format")
                    convert_cmd = [
                        "ffmpeg",
                        "-hide_banner",
                        "-y",
                        "-i",
                        temp_audio,
                        "-acodec",
                        audio_format,
                        audio_output,
                    ]

                    self._run(command=convert_cmd, fail=True)

                    # cleanup temp file
                    if os.path.exists(temp_audio):
                        os.remove(temp_audio)

                logger().debug("optimized audio extraction completed successfully")
                return

            except Exception as e:
                logger().debug(
                    f"optimized extraction failed: {str(e)}, falling back to direct conversion"
                )
                # cleanup temp file if it exists
                if os.path.exists(temp_audio):
                    try:
                        os.remove(temp_audio)
                    except:
                        pass

        # fallback: direct extraction with specified parameters
        cmd = [
            "ffmpeg",
            "-hide_banner",
            "-y",
            "-i",
            video_file,
            "-vn",  # no video
        ]

        if copy_audio:
            # direct stream copy
            cmd.extend(["-acodec", "copy"])
        elif audio_format:
            # use specified audio format
            cmd.extend(["-acodec", audio_format])

        cmd.append(audio_output)

        self._run(command=cmd, fail=False)

    def extract_video(self, *, video_file: str, video_output: str):
        """extract video without audio"""
        cmd = [
            "ffmpeg",
            "-hide_banner",
            "-y",
            "-i",
            video_file,
            "-an",  # no audio
            "-c:v",
            "copy",  # copy video stream
            video_output,
        ]

        self._run(command=cmd, fail=False)

    def combine_video_audio(
        self,
        *,
        video_file: str,
        audio_file: str,
        output_file: str,
        audio_codec: str = "aac",
    ):
        """combine video and audio files without re-encoding video"""
        cmd = [
            "ffmpeg",
            "-hide_banner",
            "-y",
            "-i",
            video_file,
            "-i",
            audio_file,
            "-map",
            "0:v:0",  # map video from first input
            "-map",
            "1:a:0",  # map audio from second input
            "-c:v",
            "copy",  # copy video stream
            "-c:a",
            audio_codec,  # specify audio codec
            "-shortest",  # match duration to shortest stream
            output_file,
        ]

        self._run(command=cmd, fail=False)

    def remove_silence(self, *, filename: str):
        tmp_filename = ""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            tmp_filename = temp_file.name
            shutil.copyfile(filename, tmp_filename)
            cmd = [
                "ffmpeg",
                "-hide_banner",
                "-y",
                "-i",
                tmp_filename,
                "-af",
                "silenceremove=stop_periods=-1:stop_duration=0.1:stop_threshold=-50dB",
                filename,
            ]
            self._run(command=cmd, fail=False)

        if os.path.exists(tmp_filename):
            os.remove(tmp_filename)

    def adjust_audio_speed(self, *, filename: str, speed: float):
        tmp_filename = ""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            tmp_filename = temp_file.name
            shutil.copyfile(filename, tmp_filename)
            cmd = [
                "ffmpeg",
                "-hide_banner",
                "-y",
                "-i",
                tmp_filename,
                "-filter:a",
                f"atempo={speed}",
                filename,
            ]
            self._run(command=cmd, fail=False)

        if os.path.exists(tmp_filename):
            os.remove(tmp_filename)

    def embed_subtitles(
        self,
        *,
        video_file: str,
        subtitles_files: List[str],
        languages_iso_639_3: List[str],
    ):
        filename = ""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            shutil.copyfile(video_file, temp_file.name)
            output_file = video_file  # modify in-place

            cmd = [
                "ffmpeg",
                "-y",  # overwrite output files without asking
                "-i",
                temp_file.name,
            ]

            # add subtitle inputs
            for subtitles_file in subtitles_files:
                cmd.extend(["-i", subtitles_file])

            # map streams
            cmd.append("-map")
            cmd.append("0")  # map all streams from the main video file
            for idx, language in enumerate(languages_iso_639_3):
                cmd.extend(
                    [
                        "-map",
                        str(idx + 1),  # map each subtitle file
                        "-c:s",
                        "mov_text",  # subtitle codec
                        "-metadata:s:s:" + str(idx),
                        f"language={language}",
                    ]
                )

            # add codecs for video and audio
            # use hardware encoder if available
            hw_encoder = self._get_hw_encoder("h264")
            if hw_encoder:
                cmd.extend(["-c:v", hw_encoder])
            else:
                cmd.extend(["-c:v", "copy"])

            cmd.extend(["-c:a", "copy", output_file])

            logger().debug(f"embed_subtitles. command: {' '.join(cmd)}")

            # run the command using the _run method
            self._run(command=cmd, fail=False)
            filename = temp_file.name

        if os.path.exists(filename):
            os.remove(filename)

    @staticmethod
    def is_ffmpeg_installed():
        cmd = ["ffprobe", "-version"]
        try:
            if (
                subprocess.run(
                    cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE
                ).returncode
                == 0
            ):
                return True
        except FileNotFoundError:
            return False
        return False

    @staticmethod
    def get_available_hwaccel_methods() -> Dict[HWAccel, bool]:
        """detect available hardware acceleration methods on the system"""
        result = {method: False for method in HWAccel}
        result[HWAccel.none] = True  # cpu is always available

        try:
            # run ffmpeg with -hwaccels option to list available hardware accelerators
            cmd = ["ffmpeg", "-hwaccels"]
            proc = subprocess.run(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True
            )

            if proc.returncode == 0:
                output = proc.stdout.lower()

                # check for each acceleration method in the output
                if "cuda" in output:
                    result[HWAccel.cuda] = True
                if "nvenc" in output or "nvidia" in output:
                    result[HWAccel.nvenc] = True
                if "qsv" in output or "quick sync" in output:
                    result[HWAccel.qsv] = True
                if "vaapi" in output:
                    result[HWAccel.vaapi] = True
                if "amf" in output or "amd" in output:
                    result[HWAccel.amf] = True

        except (subprocess.SubprocessError, FileNotFoundError):
            logger().warning("failed to detect available hardware acceleration methods")

        return result

    @staticmethod
    def get_recommended_hwaccel() -> Optional[HWAccel]:
        """get the recommended hardware acceleration method based on what's available"""
        available = FFmpeg.get_available_hwaccel_methods()

        # prioritize acceleration methods based on performance
        priorities = [
            HWAccel.cuda,
            HWAccel.nvenc,
            HWAccel.qsv,
            HWAccel.vaapi,
            HWAccel.amf,
        ]

        for accel in priorities:
            if available.get(accel, False):
                return accel

        return HWAccel.none  # fallback to cpu if no hardware acceleration is available
